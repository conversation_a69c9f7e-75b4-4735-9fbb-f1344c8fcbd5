{"name": "backend", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"dev": "node --watch index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"standard": "17.1.2"}, "eslintConfig": {"extends": "standard"}, "dependencies": {"@better-auth/client": "^0.0.2-alpha.3", "better-auth": "^1.2.7", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "express": "5.1.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.0", "zod": "^3.24.3"}}