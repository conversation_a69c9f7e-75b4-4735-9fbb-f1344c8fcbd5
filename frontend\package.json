{"name": "przone", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-select": "^2.2.5", "@tailwindcss/vite": "^4.1.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lightweight-charts": "^5.0.7", "lucide-react": "^0.513.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.5.2", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react-swc": "^3.8.0", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "tw-animate-css": "^1.3.4", "vite": "^6.2.0"}}